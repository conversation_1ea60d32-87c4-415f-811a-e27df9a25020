const API = {
    init() {
        return {
            serverTime: new Date('2025/08/19 16:30:55').getTime() / 1000,
            startTime: new Date('2025/08/18 00:00:00').getTime() / 1000,
            endTime: new Date('2025/08/24 23:59:59').getTime() / 1000,
            userInfo: {
                uid: 2567217,
                username: 'tt110489623',
                nickname: '昵称昵称昵称昵称昵称昵称昵称昵称',
                alias: '110489623',
            },
        };
    },
    userRank({ page = 1, pageSize = 10 }) {
        return {
            list: Array(pageSize)
                .fill(0)
                .map((_, i) => ({
                    userInfo: {
                        uid: 2567217,
                        username: `tt110489623`,
                        nickname: '昵称昵称昵称昵昵称昵称昵称昵昵称昵称昵称昵',
                        alias: '110489623',
                    },
                    channelInfo: {
                        channelId: 4324233,
                        // status: window.Mock.Random.integer(0, 4),
                        status: 0,
                    },
                    rank: i + 1 + (page - 1) * 10,
                    value: 199 * (i + 1 + (page - 1) * 10),
                })),
            me: {
                userInfo: {
                    uid: 2567217,
                    nickname: '昵称昵称昵称昵',
                    username: 'tt110489623',
                    alias: '110489623',
                },
                rank: 10,
                value: 9999,
            },
            top1SupportList: Array(10)
                .fill(0)
                .map(() => ({
                    uid: 2567217,
                    username: `tt110489623`,
                    nickname: '昵称昵称昵称昵昵称昵称昵称昵昵称昵称昵称昵',
                    alias: '110489623',
                    sex: window.Mock.Random.integer(0, 1),
                })),
            total: 100,
        };
    },
};

const getMockData = (type, payload) =>
    new Promise((resolve) => {
        const delay = Math.round(Math.random() * 10) * 50; // 模拟访问延迟
        let data;
        if (typeof API[type] === 'function')
            data = API[type](payload);
        else data = API[type];

        // eslint-disable-next-line no-console
        console.log(
            `模拟接口请求名称<=== ${type} delay: ${delay} ms; payload: ${JSON.stringify(payload)}`,
        );
        window.setTimeout(() => {
            // eslint-disable-next-line no-console
            console.log('模拟接口请求返回===>', data);
            resolve({
                code: 0,
                data,
                msg: '',
            });
        }, delay);
    });
export default getMockData;
