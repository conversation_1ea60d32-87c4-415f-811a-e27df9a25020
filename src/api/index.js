// 执行pb.cjs命令后会覆盖掉手写的代码，请谨慎操作!
import request from '../utils/request';
import getMockData from './mockData';

export const fetchApi = ({
    proPrefix = '/activity.Activity/',
    api,
    data = {},
    config = {},
}) => {
    const { mock } = myWebview.params;
    if (mock)
        return to(getMockData(api, data));

    const url = `${proPrefix}/${api}`.replace('//', '/');
    return to(request.post(url, data, config));
};

/**
 * ********************************
 * *********  活动接口 *************
 * ********************************
 */
const REQUEST_API_MAP = {
    init: 'init',
};

/** @type {function(import('./api.d.ts').InitReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').InitResp},any]>} */
export const init = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.init, data, config });

export default {
    init,
};
