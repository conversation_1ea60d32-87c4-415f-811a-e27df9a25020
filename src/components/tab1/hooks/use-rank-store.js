import { defineStore } from 'pinia';
import { rankTab } from '../const';
import useDateSelectStore from './use-date-select-store';
import { rankList } from '@/api';

function getMountedTabKey() {
    if (myWebview.params.send) {
        return rankTab.send.key;
    }
    if (myWebview.params.receive) {
        return rankTab.receive.key;
    }
    return rankTab.send.key;
}

const useRankStore = defineStore('rank', () => {
    const state = reactive({
        tabKey: getMountedTabKey(),
        rankList: [],
        self: {},
    });

    const dateSelectStore = useDateSelectStore();

    const isSend = computed(() => state.tabKey === rankTab.send.key);

    const pageConfig = {
        page: 1,
        size: 10,
        totalPage: 1,
    };

    const initPageConfig = () => {
        pageConfig.page = 1;
        pageConfig.totalPage = 1;
    };

    const getRankList = async (payload) => {
        const loading = showLoading();
        const [{ code, data }] = await rankList(payload);
        loading.close();
        if (code === 0) {
            return data;
        }
        return {};
    };

    let isIniting = false;

    const setTab = (key) => {
        if (isIniting)
            return;
        state.tabKey = key;
    };

    const initRankList = async () => {
        if (isIniting)
            return;
        isIniting = true;
        initPageConfig();
        const { page, size } = pageConfig;
        const { total, list, self } = await getRankList({ page, size, type: state.tabKey, ...(dateSelectStore.currentTab === 'total' ? {} : { date: dateSelectStore.currentTab }) });
        pageConfig.totalPage = Math.ceil(total / size) || 1;
        state.rankList = list || [];
        state.self = self || {};
        isIniting = false;
    };

    let isLoadiing = false;
    const loadMore = async () => {
        if (isLoadiing || pageConfig.page >= pageConfig.totalPage)
            return;
        isLoadiing = true;
        pageConfig.page += 1;
        const { page, size } = pageConfig;
        const { list } = await getRankList({ page, size, type: state.tabKey, ...(dateSelectStore.currentTab === 'total' ? {} : { date: dateSelectStore.currentTab }) });
        state.rankList = state.rankList.concat(list || []);
        isLoadiing = false;
    };

    return {
        ...toRefs(state),
        setTab,
        isSend,
        getRankList,
        initRankList,
        loadMore,
    };
});

export default useRankStore;
