<template>
    <div>
        <div class="record-box">
            <p>·达人每日完成活动任务可获得星光值</p>
            <p>·学员考核S/A级的队伍可得20%/10%加成</p>
            <img
                class="btn"
                :src="requireImg('tab1/<EMAIL>')"
                alt=""
                @click="useEventBus('my-record-popup').emit({ show: true })">
        </div>
        <Total-day-select
            class="total-day-select-box"
            @change-date="changeDate" />
        <div
            class="area-box">
            <div
                v-for="(item, index) in areaMap"
                :class="{ active: areaIndex === index }"
                @click="changeArea(index)">
                {{ item.name }}
            </div>
        </div>
        <div class="reward-box total_bg">
            <p>需成功组队后才可上榜,接活动期间队伍累计星光值排名</p>
        </div>
        <tab1-rank-item />
        <unteamed-rank-me />
        <my-record-popup />
        <team-up-popup />
        <confirm-team-dialog />
    </div>
</template>

<script setup>
import useInitStore from '@/stores/modules/use-init-store';

const { serverTime } = storeToRefs(useInitStore());
const changeDate = () => {

};
const areaMap = [
    {
        name: 'A区',
        value: 1,
    },
    {
        name: 'B区',
        value: 1,
    },
    {
        name: 'C区',
        value: 1,
    },
    {
        name: 'D区',
        value: 1,
    },
    {
        name: 'E区',
        value: 1,
    },
];
const areaIndex = ref(0);
const changeArea = (index) => {
    areaIndex.value = index;
};
onMounted(() => {
    console.log('serverTime22', serverTime.value);
});
</script>

<style lang="less" scoped>
.record-box {
    .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 350px, 49px);
    margin: 0 auto;
    position: relative;
    padding-left: 63px;
    font-size: 12px;
    font-weight: normal;
    color: #ffebc7;
    display: flex;
    justify-content: center;
    flex-direction: column;
    p {
        line-height: 20px;
    }
    .btn {
        width: 77px;
        height: 20px;
        position: absolute;
        right: 0;
        top: 0;
        z-index: 2;
    }
}
.reward-box {
    width: 375px;
    height: 119px;
    padding-left: 50px;
    padding-top: 8px;
    p {
        color: #ffebc7;
        font-size: 11px;
    }
}
.total_bg {
    .full-bg('@/assets/img/tab1/<EMAIL>');
}
.area-box {
    .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 375px, 26px);
    display: flex;
    justify-content: center;
    align-items: center;
    div {
        width: 60px;
        text-align: center;
        color: #bba6a1;
        font-size: 12px;
    }
    div.active {
        color: #fdff74;
    }
}
</style>
