<template>
    <popup-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
    >
        <div class="team-up-popup">
            <div class="top">
                <img
                    class="avatar"
                    :src="requireImg('default_avatar_no_compress.png')" />我的身份：导师<img
                        class="icon"
                        :src="requireImg('tab1/<EMAIL>')" />
            </div>
            <div class="topbar">
                <div class="tab">
                    <p
                        :class="{ active: tabIndex === 0 }"
                        @click="changeTab(0)">
                        我发出邀请
                    </p>
                    <p
                        :class="{ active: tabIndex === 1 }"
                        @click="changeTab(1)">
                        我收到邀请
                    </p>
                </div>
                <div class="search">
                    <input
                        type="text"
                        placeholder="输入TTID搜索">
                </div>
            </div>
            <div class="user-box">
                <div
                    v-for="item in 20"
                    class="user">
                    <div class="user-pic">
                        <img
                            class="avatar"
                            :src="requireImg('default_avatar_no_compress.png')"
                            alt="">
                    </div>
                    <div class="user-name">放的开飞机上看见都是分开</div>
                    <img
                        class="btn"
                        :src="requireImg('tab1/<EMAIL>')"
                        @click="useEventBus('confirm-team-dialog').emit({ show: true })" />
                </div>
            </div>
        </div>
    </popup-container>
</template>

<script setup>
const isShow = ref(false);
const tabIndex = ref(0);
const changeTab = (index) => {
    tabIndex.value = index;
};
useEventBus('team-up-popup').on(({ show = true }) => {
    isShow.value = show;
});
</script>

<style lang="less" scoped>
.team-up-popup {
    .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 375px, 428px);
    padding-top: 55px;
    .top {
        width: 100%;
        display: flex;
        justify-content: center;
        font-size: 16px;
        font-weight: normal;
        text-align: left;
        color: #ffe29c;
        height: 28px;
        align-items: center;
        margin-bottom: 30px;
        .avatar {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            margin-right: 6px;
        }
        .icon {
            width: 13px;
            height: 13px;
            margin-left: 4px;
            margin-top: -10px;
        }
    }
    .topbar {
        width: 100%;
        display: flex;
        justify-content: center;
        .tab {
            display: flex;
            justify-content: center;
            font-size: 13px;
            font-weight: normal;
            text-align: center;
            color: #bba6a1;
            height: 24px;
            line-height: 24px;
            margin-right: 20px;
            p {
                width: 80px;
            }
            .active {
                color: #fdff74;
                font-size: 14px;
            }
        }
        .search {
            .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 124px, 24px);
            padding-left: 4px;
            input {
                background: none;
                width: 90px;
                height: 24px;
                border: none;
                font-size: 12px;
                color: #fff;
            }
        }
    }
    .user-box {
        width: 100%;
        display: flex;
        justify-content: center;
        flex-flow: wrap;
        padding-top: 20px;
        .user {
            width: 70px;
            margin: 0 10px 14px;
            &-pic {
                width: 50px;
                height: 50px;
                // .full-bg('@/assets/img/tab1/<EMAIL>');
                display: flex;
                justify-content: center;
                align-items: center;
                margin: 0 auto 3px;
                .avatar {
                    max-width: 100%;
                    max-height: 100%;
                    border-radius: 50%;
                }
            }
            &-name {
                font-size: 11px;
                font-weight: 400;
                text-align: center;
                color: #ffebc7;
                width: 100%;
                .one-line();
                margin-bottom: 8px;
            }
            .btn {
                width: 64px;
                height: 28px;
                margin: 0 auto;
            }
        }
    }
}
</style>
