<template>
    <modal-container
        v-model:show="isShow"
        :close-on-click-overlay="true">
        <div class="confirm-team-dialog">
            <p>确认接受主播昵称...的组队邀请吗? </p>
            <p>组队成功后将不可更改!</p>
            <div class="input-box">
                <input
                    type="text"
                    placeholder="请手动输入“接受邀请”并点击确认完成 组队操作">
                <p>一键写入</p>
            </div>
        </div>
    </modal-container>
</template>

<script setup>
import { checkInstallApp } from '@/utils/jsbridge';
import SVG from '@/config/svg';
import { urlConfig } from '@/config/url-config';
import { afterVersion } from '@/utils';

const isShow = ref(false);

useEventBus('confirm-team-dialog').on(({ show = true }) => {
    isShow.value = show;
});
</script>

<style lang="less" scoped>
.confirm-team-dialog {
    width: 375px;
    height: 220px;
    padding-top: 52px;
    .full-bg('@/assets/img/tab1/<EMAIL>');
    .input-box {
        width: 200px;
        margin: 0 auto;
    }
}
</style>
