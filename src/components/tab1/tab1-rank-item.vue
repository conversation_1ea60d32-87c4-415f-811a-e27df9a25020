<template>
    <div class="rank-item">
        <div class="left">
            <div class="sort">3</div>
            <div class="user">
                <div class="user-item">
                    <div class="pic">
                        <img
                            class="avatar"
                            :src="requireImg('default_avatar_no_compress.png')" />
                    </div>
                    <div class="nickname">用户昵称嘻嘻嘻谢谢谢谢谢谢谢谢</div>
                </div>
                <img
                    class="icon"
                    :src="requireImg('tab1/<EMAIL>')" />
                <div class="user-item">
                    <div class="pic">
                        <img
                            class="avatar"
                            :src="requireImg('default_avatar_no_compress.png')" />
                    </div>
                    <div class="nickname">用户昵称嘻嘻嘻谢谢谢谢谢谢谢谢</div>
                </div>
            </div>
        </div>
        <div class="right">
            <div class="value">
                <img
                    class="icon"
                    :src="requireImg('tab1/<EMAIL>')" />+9.99W
            </div>
        </div>
    </div>
</template>

<script setup>

</script>

<style lang="less" scoped>
    .rank-item {
    .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 350px, 80px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 auto 10px;
    .left {
        display: flex;
        justify-content: flex-start;
        .sort {
            width: 40px;
            text-align: center;
            font-size: 13px;
            font-weight: 700;
            text-align: center;
            color: #e8dec2;
        }
        .user {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .icon {
                width: 27px;
                height: 27px;
            }
            &-item {
                width: 69px;
                .pic {
                    width: 46px;
                    height: 46px;
                    position: relative;
                    margin: 0 auto 8px;
                    .avatar {
                        width: 46px;
                        height: 46px;
                        border-radius: 50%;
                    }
                }
                .nickname {
                    width: 100%;
                    .one-line();
                    font-size: 12px;
                    font-weight: 400;
                    text-align: left;
                    color: #e5e5e5;
                }
            }
        }
    }
    .right {
        .value {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            height: inherit;
            margin-top: 8px;
            padding-right: 10px;
            .icon {
                width: 16px;
                height: 16px;
                margin-right: 2px;
            }
            font-size: 14px;
            font-weight: normal;
            text-align: left;
            color: #ffe29c;
        }
    }
}
</style>
