<template>
    <popup-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
    >
        <div class="my-record-popup">
            <div class="top">
                <img
                    class="avatar"
                    :src="requireImg('default_avatar_no_compress.png')" />队伍加成比例: 10%
            </div>
            <task-day-select />
            <div class="task-box">
                <p class="task-title">- 达人基础活跃 -</p>
                <div class="task-item">
                    <div class="left">
                        <p class="name">听听时长每累计60min=200星光值</p>
                        <p class="gift">【豆豆礼物名】可在礼物架送出</p>
                        <p class="progress">今日进度: <span>120min</span></p>
                    </div>
                    <div class="right">
                        <div class="label">每日限20次</div>
                        <p class="value">
                            <img
                                class="icon"
                                :src="requireImg('tab1/<EMAIL>')" />+9.99W
                        </p>
                    </div>
                </div>
                <p class="task-title">- 限定礼物收集 -</p>
                <div class="task-item">
                    <div class="left">
                        <p class="name">听听时长每累计60min=200星光值</p>
                        <p class="gift">【豆豆礼物名】可在礼物架送出</p>
                        <p class="progress">今日进度: <span>120min</span></p>
                    </div>
                    <div class="right">
                        <div class="label">每日限20次</div>
                        <p class="value">
                            <img
                                class="icon"
                                :src="requireImg('tab1/<EMAIL>')" /> + 9.99W
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </popup-container>
</template>

<script setup>
const isShow = ref(false);

useEventBus('my-record-popup').on(({ show = true }) => {
    isShow.value = show;
});
</script>

<style lang="less" scoped>
.my-record-popup {
    .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 375px, 628px);
    padding-top: 5px;
    .top {
        width: 100%;
        display: flex;
        justify-content: flex-end;
        font-size: 16px;
        font-weight: normal;
        text-align: left;
        color: #ffe29c;
        height: 26px;
        align-items: center;
        padding-right: 12px;
        .avatar {
            width: 26px;
            height: 26px;
            border-radius: 50%;
            border: 2px solid #ccc;
            margin-right: 4px;
        }
    }
    .task-box {
        width: 350px;
        height: 554px;
        background: #433d5d;
        border-radius: 10px 10px 0px 0px;
        margin: 0 auto;
        padding-top: 14px;
    }
    .task-title {
        font-size: 14px;
        font-weight: normal;
        text-align: center;
        color: #d4d4d4;
        margin-bottom: 9px;
    }
    .task-item {
        width: 338px;
        height: 57px;
        background: #38334e;
        border-radius: 10px;
        margin: 0 auto 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-left: 10px;
        .left {
            .name {
                font-size: 12px;
                font-weight: 500;
                text-align: left;
                color: #ffebc7;
                margin-bottom: 3px;
            }
            .gift {
                font-size: 9px;
                font-weight: 500;
                text-align: left;
                color: #e360ff;
                margin-bottom: 2px;
            }
            .progress {
                font-size: 12px;
                font-weight: 500;
                text-align: left;
                color: #e5e5e5;
                span {
                    color: #48f0ff;
                }
            }
        }
        .right {
            position: relative;
            height: inherit;
            .value {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                height: inherit;
                margin-top: 8px;
                padding-right: 10px;
                .icon {
                    width: 16px;
                    height: 16px;
                    margin-right: 2px;
                }
                font-size: 14px;
                font-weight: normal;
                text-align: left;
                color: #ffe29c;
            }
            .label {
                .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 60px, 17px);
                font-size: 10px;
                font-weight: 500;
                text-align: left;
                color: #e5e5e5;
                line-height: 17px;
                position: absolute;
                right: 0;
                top: 0;
                z-index: 2;
            }
        }
    }
}
</style>
